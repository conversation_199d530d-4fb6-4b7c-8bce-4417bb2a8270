import { Hospital } from './hospital.model';
import { PROFILE_TYPES } from '../constants/defaults.consts';
import {City} from "./city.model";
import {Country} from "./country.model";
// import {Specialty} from './specialty.model';
// import {ANESTHETIST_TYPES, NURSE_TYPES} from '../const/glabals.const';
// import {Calendar} from './calendar.model';

export interface Profile {
  _id?: string;
  assignedID?: string;
  email?: string;
  title?: string;
  position?: string;
  gender?: string;
  residency?: string;
  seniority?: string;
  phoneNumber?: string;
  adress?: string;
  isAdmin?: boolean;
  firstName?: string;
  lastName?: string;
  startingDate?: Date;
  hospital?: Hospital;
  specialty?: any;
  profilePic?: string;
  allergies?: string[];
  chronicDiseases?: string[];
  permanentDrugs?: string[];
  deletedAt?: Date;
  updatedBy?: Profile;
  createdBy?: Profile;
  sessionCounts?: number;
  birthDate?: Date;
  insurance?: string;
  insuranceId?: string;
  doctors?: string[];
  receptionits?: string[];
  allDoctors?: boolean;
  allReceptionits?: boolean;
  language?: string;

  city?: City;
  country?: Country;
  username?: string;

  address2?: string;
  address3?: string;
  staffId?: string;

  staff?: {
    _id?: string;
  };


  height?: string;
  weight?: string;

}


export function isDoctor(profile: Profile) {
  return profile.title === PROFILE_TYPES.doctor;
}

export function isSuperAdmin(profile: Profile) {
  return profile.title === PROFILE_TYPES.superAdmin;
}

export function isAssistant(profile: Profile) {
  return profile.title === PROFILE_TYPES.assistant;
}

export function isStaff(profile: Profile) {
  return [PROFILE_TYPES.doctor, PROFILE_TYPES.assistant].includes(
    profile.title as string
  );
}
